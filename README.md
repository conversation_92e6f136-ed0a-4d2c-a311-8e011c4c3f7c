![Nylo Banner](https://nylo.dev/images/nylo_logo_header.png)

<p align="center">
  <a href="https://github.com/nylo-core/nylo/releases"><img src="https://img.shields.io/github/v/release/nylo-core/nylo?style=plastic" alt="Latest Release Version"></a>
  <a href="https://github.com/nylo-core/nylo/blob/master/LICENSE"><img alt="GitHub" src="https://img.shields.io/github/license/nylo-core/nylo?style=plastic"></a>
  <a href="#"><img alt="GitHub stars" src="https://img.shields.io/github/stars/nylo-core/nylo?style=plastic"></a>
</p>

## Nylo

Nylo is a micro-framework for Flutter which is designed to help simplify developing apps. Every project provides a simple boilerplate and MVC pattern to help you build apps easier. 

This project is open source and MIT-licenced, we welcome any contributions. You can join as a backer/sponsor to fund future development for this project [here](https://nylo.dev)

---

## Features
Some core features available
* [Routing](https://nylo.dev/docs/6.x/router).
* [Themes and styling](https://nylo.dev/docs/6.x/themes-and-styling).
* [Localization](https://nylo.dev/docs/6.x/localization).
* [CLI for generating project files](https://nylo.dev/docs/6.x/metro).
* [Elegant API Services for Networking](https://nylo.dev/docs/6.x/networking).
* [Creating App Icons](https://nylo.dev/docs/6.x/app-icons).
* [Project Configuration](https://nylo.dev/docs/6.x/configuration).
* [Streamlined Project Structure](https://nylo.dev/docs/6.x/directory-structure).

## Requirements
* Flutter >= 3.24.0

## Getting Started

``` bash
git clone https://github.com/nylo-core/nylo.git
```

## Documentation
View our [docs](https://nylo.dev/docs) and visit [nylo.dev](https://nylo.dev)

## Changelog
Please see [CHANGELOG](https://github.com/nylo-core/framework/blob/6.x/CHANGELOG.md) for more information what has changed recently.

## Social
* [Twitter](https://twitter.com/nylo_dev)

## Security
If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## Contributors
* [Anthony Gordon](https://github.com/agordn52)
* [lpdevit](https://github.com/lpdevit)
* [Abdulrasheed1729](https://github.com/Abdulrasheed1729)
* [Rashid-Khabeer](https://github.com/Rashid-Khabeer)
* [youssefKadaouiAbbassi](https://github.com/youssefKadaouiAbbassi)
* [jeremyhalin](https://github.com/jeremyhalin)
* [abdulawalarif](https://github.com/abdulawalarif)
* [lepresk](https://github.com/lepresk)
* [joshua1996](https://github.com/joshua1996)
* [stensonb](https://github.com/stensonb)
* [ruwiss](https://github.com/ruwiss)
* [rytisder](https://github.com/rytisder)
* [necro304](https://github.com/necro304)
* [israelins85](https://github.com/israelins85)
* [voytech-net](https://github.com/voytech-net)
* [sadobass](https://github.com/sadobass)

## Contributing

Please see <a href="https://nylo.dev/docs/6.x/contributions" target="_BLANK">CONTRIBUTING</a> for details.

## Licence

The MIT License (MIT). Please view the [License](https://github.com/nylo-core/nylo/blob/master/licence) File for more information.
