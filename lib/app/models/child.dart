import 'package:nylo_framework/nylo_framework.dart';

class Child extends Model {
  String? id;
  String? name;
  String? grade;
  String? iconPath;
  bool? isSelected;

  Child({
    this.id,
    this.name,
    this.grade,
    this.iconPath,
    this.isSelected = false,
  });

  Child.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    grade = json['grade'];
    iconPath = json['icon_path'];
    isSelected = json['is_selected'] ?? false;
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'grade': grade,
      'icon_path': iconPath,
      'is_selected': isSelected,
    };
  }

  @override
  String toString() {
    return 'Child{id: $id, name: $name, grade: $grade, iconPath: $iconPath, isSelected: $isSelected}';
  }
}
