import 'package:nylo_framework/nylo_framework.dart';
import 'transport_mode.dart';
import 'child.dart';

class PickupSession extends Model {
  String? id;
  String? userId;
  Child? selectedChild;
  TransportMode? transportMode;
  DateTime? startTime;
  String? status; // 'started', 'en_route', 'in_queue', 'completed', 'cancelled'
  int? queuePosition;
  int? estimatedWaitTime; // in minutes
  double? latitude;
  double? longitude;

  PickupSession({
    this.id,
    this.userId,
    this.selectedChild,
    this.transportMode,
    this.startTime,
    this.status = 'started',
    this.queuePosition,
    this.estimatedWaitTime,
    this.latitude,
    this.longitude,
  });

  PickupSession.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    selectedChild = json['selected_child'] != null 
        ? Child.fromJson(json['selected_child']) 
        : null;
    transportMode = json['transport_mode'] != null 
        ? TransportMode.values.firstWhere(
            (e) => e.name == json['transport_mode'],
            orElse: () => TransportMode.car,
          )
        : null;
    startTime = json['start_time'] != null 
        ? DateTime.parse(json['start_time']) 
        : null;
    status = json['status'];
    queuePosition = json['queue_position'];
    estimatedWaitTime = json['estimated_wait_time'];
    latitude = json['latitude']?.toDouble();
    longitude = json['longitude']?.toDouble();
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'selected_child': selectedChild?.toJson(),
      'transport_mode': transportMode?.name,
      'start_time': startTime?.toIso8601String(),
      'status': status,
      'queue_position': queuePosition,
      'estimated_wait_time': estimatedWaitTime,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  bool get isActive => status != 'completed' && status != 'cancelled';
  
  bool get isInQueue => status == 'in_queue';
  
  bool get isCompleted => status == 'completed';

  @override
  String toString() {
    return 'PickupSession{id: $id, userId: $userId, selectedChild: $selectedChild, transportMode: $transportMode, status: $status}';
  }
}
