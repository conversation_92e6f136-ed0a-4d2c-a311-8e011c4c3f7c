import 'package:nylo_framework/nylo_framework.dart';

class User extends Model {
  String? id;
  String? name;
  String? email;
  String? phoneNumber;
  bool? rememberPhone;
  List<String>? childrenIds;

  static StorageKey key = 'user';

  User() : super(key: key);

  User.fromJson(dynamic data) {
    id = data['id'];
    name = data['name'];
    email = data['email'];
    phoneNumber = data['phone_number'];
    rememberPhone = data['remember_phone'] ?? false;
    childrenIds = data['children_ids']?.cast<String>();
  }

  @override
  toJson() => {
    "id": id,
    "name": name,
    "email": email,
    "phone_number": phoneNumber,
    "remember_phone": rememberPhone,
    "children_ids": childrenIds,
  };
}
