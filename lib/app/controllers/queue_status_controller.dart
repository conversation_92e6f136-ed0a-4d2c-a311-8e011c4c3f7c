import 'dart:async';
import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/pickup_session.dart';
import 'controller.dart';

class QueueStatusController extends Controller {
  PickupSession? currentSession;
  bool isLoading = false;
  Timer? _updateTimer;

  @override
  construct(BuildContext context) {
    super.construct(context);
    _loadSession();
    _startPeriodicUpdates();
  }

  /// Load current pickup session
  _loadSession() async {
    setState(() {
      isLoading = true;
    });

    try {
      Map<String, dynamic>? sessionData = await NyStorage.read('current_pickup_session');
      
      if (sessionData != null) {
        currentSession = PickupSession.fromJson(sessionData);
        
        // Update session status to in_queue
        if (currentSession!.status != 'in_queue') {
          currentSession!.status = 'in_queue';
          currentSession!.queuePosition = 5; // Mock initial position
          currentSession!.estimatedWaitTime = 15; // Mock initial wait time
          await storageSave('current_pickup_session', currentSession!.toJson());
        }
      } else {
        // No session found, redirect to main
        routeTo('/main', removeUntilPredicate: (route) => false);
        return;
      }
      
    } catch (e) {
      showToastDanger(description: 'Error al cargar la sesión');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Start periodic updates to simulate queue movement
  _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(Duration(seconds: 10), (timer) {
      _updateQueueStatus();
    });
  }

  /// Update queue status (mock implementation)
  _updateQueueStatus() async {
    if (currentSession == null) return;

    // Simulate queue movement
    if (currentSession!.queuePosition != null && currentSession!.queuePosition! > 0) {
      currentSession!.queuePosition = currentSession!.queuePosition! - 1;
      
      // Update estimated wait time
      currentSession!.estimatedWaitTime = currentSession!.queuePosition! * 3;
      
      // If reached front of queue, complete the session
      if (currentSession!.queuePosition! <= 0) {
        currentSession!.status = 'completed';
        _updateTimer?.cancel();
        
        // Auto-navigate to confirmation after a short delay
        Future.delayed(Duration(seconds: 2), () {
          routeTo('/confirmation');
        });
      }
      
      // Save updated session
      await storageSave('current_pickup_session', currentSession!.toJson());
      setState(() {});
    }
  }

  /// Cancel pickup and leave queue
  onCancel() async {
    _updateTimer?.cancel();
    
    if (currentSession != null) {
      currentSession!.status = 'cancelled';
      await storageSave('current_pickup_session', currentSession!.toJson());
    }
    
    showToastSuccess(description: 'Recogida cancelada');
    routeTo('/main');
  }

  /// Get queue status color based on position
  Color getQueueStatusColor() {
    if (currentSession?.queuePosition == null) return Colors.grey;
    
    int position = currentSession!.queuePosition!;
    if (position <= 1) return Colors.green;
    if (position <= 3) return Colors.orange;
    return Colors.red;
  }

  /// Get queue status text
  String getQueueStatusText() {
    if (currentSession?.queuePosition == null) return "Calculando...";
    
    int position = currentSession!.queuePosition!;
    if (position <= 0) return "¡Es tu turno!";
    if (position == 1) return "Muy cerca";
    if (position <= 3) return "Cerca";
    return "Lejos";
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    super.dispose();
  }
}
