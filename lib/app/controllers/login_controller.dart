import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/user.dart';
import 'controller.dart';

class LoginController extends Controller {
  final TextEditingController phoneController = TextEditingController();
  bool rememberPhone = false;
  bool isLoading = false;

  @override
  construct(BuildContext context) {
    super.construct(context);
    _loadSavedPhone();
  }

  /// Load saved phone number if user chose to remember it
  _loadSavedPhone() async {
    String? savedPhone = await NyStorage.read('saved_phone');
    bool? shouldRemember = await NyStorage.read('remember_phone');

    if (savedPhone != null && shouldRemember == true) {
      phoneController.text = savedPhone;
      rememberPhone = true;
      updateState(() {});
    }
  }

  /// Validate phone number format
  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Por favor ingresa tu número de teléfono';
    }
    
    // Remove any non-digit characters for validation
    String cleanPhone = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanPhone.length < 8) {
      return 'El número debe tener al menos 8 dígitos';
    }
    
    return null;
  }

  /// Handle login with phone number
  onLogin() async {
    String phone = phoneController.text.trim();
    
    // Validate phone
    String? validation = validatePhone(phone);
    if (validation != null) {
      showToastDanger(description: validation);
      return;
    }

    isLoading = true;
    updateState(() {});

    try {
      // Save phone if user wants to remember it
      if (rememberPhone) {
        await storageSave('saved_phone', phone);
        await storageSave('remember_phone', true);
      } else {
        await NyStorage.delete('saved_phone');
        await storageSave('remember_phone', false);
      }

      // Create or update user
      User user = User();
      user.phoneNumber = phone;
      user.rememberPhone = rememberPhone;
      
      // Save user to storage
      await user.save();

      // Navigate to main screen
      routeTo('/main', removeUntilPredicate: (route) => false);
      
    } catch (e) {
      showToastDanger(description: 'Error al iniciar sesión. Intenta nuevamente.');
    } finally {
      isLoading = false;
      updateState(() {});
    }
  }

  /// Toggle remember phone option
  onToggleRememberPhone(bool? value) {
    rememberPhone = value ?? false;
    updateState(() {});
  }


}
