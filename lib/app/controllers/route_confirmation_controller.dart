import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/pickup_session.dart';
import '/app/models/transport_mode.dart';
import 'controller.dart';

class RouteConfirmationController extends Controller {
  PickupSession? currentSession;
  bool isLoading = false;
  String? userLocation;

  @override
  construct(BuildContext context) {
    super.construct(context);
    _loadSession();
  }

  /// Load current pickup session
  _loadSession() async {
    setState(() {
      isLoading = true;
    });

    try {
      Map<String, dynamic>? sessionData = await NyStorage.read('current_pickup_session');
      
      if (sessionData != null) {
        currentSession = PickupSession.fromJson(sessionData);
        
        // Get user location if coming by car
        if (currentSession?.transportMode == TransportMode.car) {
          await _getUserLocation();
        }
      } else {
        // No session found, redirect to main
        routeTo('/main', removeUntilPredicate: (route) => false);
        return;
      }
      
    } catch (e) {
      showToastDanger(description: 'Error al cargar la sesión');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Get user's current location (mock implementation)
  _getUserLocation() async {
    // TODO: Implement actual location service
    // For now, using mock location
    userLocation = "Av. Principal 123, cerca del colegio";
    setState(() {});
  }

  /// Notify school of arrival
  onNotifySchool() async {
    if (currentSession == null) return;

    setState(() {
      isLoading = true;
    });

    try {
      // Update session status
      currentSession!.status = 'en_route';
      await storageSave('current_pickup_session', currentSession!.toJson());

      if (currentSession!.transportMode == TransportMode.car) {
        // For car users, go to queue status
        routeTo('/queue-status');
      } else {
        // For walking users, go directly to confirmation
        currentSession!.status = 'completed';
        await storageSave('current_pickup_session', currentSession!.toJson());
        routeTo('/confirmation');
      }
      
    } catch (e) {
      showToastDanger(description: 'Error al notificar al colegio');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// Cancel pickup
  onCancel() async {
    if (currentSession != null) {
      currentSession!.status = 'cancelled';
      await storageSave('current_pickup_session', currentSession!.toJson());
    }
    
    routeTo('/main');
  }

  /// Get appropriate message based on transport mode
  String get confirmationMessage {
    if (currentSession?.transportMode == TransportMode.car) {
      return "Confirma tu ubicación y avisa al colegio cuando estés listo";
    } else {
      return "Avisamos que estás cerca. ¡Nos preparamos!";
    }
  }

  /// Get appropriate button text based on transport mode
  String get buttonText {
    if (currentSession?.transportMode == TransportMode.car) {
      return "Avisar al colegio";
    } else {
      return "Confirmar llegada";
    }
  }
}
