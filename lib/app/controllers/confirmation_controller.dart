import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/pickup_session.dart';
import 'controller.dart';

class ConfirmationController extends Controller {
  PickupSession? currentSession;
  bool isLoading = false;

  @override
  construct(BuildContext context) {
    super.construct(context);
    _loadSession();
  }

  /// Load current pickup session
  _loadSession() async {
    isLoading = true;
    updateState();

    try {
      Map<String, dynamic>? sessionData = await NyStorage.read('current_pickup_session');

      if (sessionData != null) {
        currentSession = PickupSession.fromJson(sessionData);

        // Ensure session is marked as completed
        if (currentSession!.status != 'completed') {
          currentSession!.status = 'completed';
          await storageSave('current_pickup_session', currentSession!.toJson());
        }
      } else {
        // No session found, redirect to main
        routeTo('/main', removeUntilPredicate: (route) => false);
        return;
      }

    } catch (e) {
      showToastDanger(description: 'Error al cargar la sesión');
    } finally {
      isLoading = false;
      updateState();
    }
  }

  /// Finish pickup process and return to main screen
  onFinish() async {
    // Clear current session
    await NyStorage.delete('current_pickup_session');
    
    // Show success message
    showToastSuccess(
      title: "¡Recogida completada!",
      description: "Que tengas un buen día"
    );
    
    // Return to main screen
    routeTo('/main', removeUntilPredicate: (route) => false);
  }

  /// Return to main screen without clearing session (for review)
  onReturnToMain() {
    routeTo('/main');
  }

  /// Get confirmation message with child's name
  String get confirmationMessage {
    String childName = currentSession?.selectedChild?.name ?? "tu hijo/a";
    return "Ya avisamos al colegio. Prepararemos a $childName.";
  }

  /// Get pickup completion time
  String get completionTime {
    if (currentSession?.startTime != null) {
      DateTime now = DateTime.now();
      Duration duration = now.difference(currentSession!.startTime!);
      
      if (duration.inMinutes < 1) {
        return "Completado en menos de 1 minuto";
      } else if (duration.inMinutes == 1) {
        return "Completado en 1 minuto";
      } else {
        return "Completado en ${duration.inMinutes} minutos";
      }
    }
    return "Completado exitosamente";
  }
}
