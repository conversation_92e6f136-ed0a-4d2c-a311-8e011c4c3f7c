import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/user.dart';
import '/app/models/child.dart';
import '/app/models/transport_mode.dart';
import '/app/models/pickup_session.dart';
import 'controller.dart';

class MainController extends Controller {
  User? currentUser;
  List<Child> children = [];
  Child? selectedChild;
  TransportMode selectedTransportMode = TransportMode.car;
  bool isLoading = false;

  @override
  construct(BuildContext context) {
    super.construct(context);
    _loadUserData();
  }

  /// Load current user and their children
  _loadUserData() async {
    isLoading = true;
    updateState(() {});

    try {
      // Load user from storage
      currentUser = await User().find();

      if (currentUser == null) {
        // User not found, redirect to login
        routeTo('/login', removeUntilPredicate: (route) => false);
        return;
      }

      // Load children data (for now, using mock data)
      await _loadChildren();

    } catch (e) {
      showToastDanger(description: 'Error al cargar datos del usuario');
    } finally {
      isLoading = false;
      updateState();
    }
  }

  /// Load children data (mock data for now)
  _loadChildren() async {
    // TODO: Replace with actual API call
    children = [
      Child(
        id: '1',
        name: 'María González',
        grade: '3° Grado',
        iconPath: 'child_girl',
      ),
      Child(
        id: '2',
        name: 'Juan González',
        grade: '1° Grado',
        iconPath: 'child_boy',
      ),
    ];
    
    // Select first child by default
    if (children.isNotEmpty) {
      selectedChild = children.first;
      selectedChild!.isSelected = true;
    }
    
    updateState();
  }

  /// Select a child for pickup
  onSelectChild(Child child) {
    // Deselect all children
    for (var c in children) {
      c.isSelected = false;
    }

    // Select the chosen child
    child.isSelected = true;
    selectedChild = child;
    updateState();
  }

  /// Change transport mode
  onChangeTransportMode(TransportMode mode) {
    selectedTransportMode = mode;
    updateState();
  }

  /// Start pickup process
  onStartPickup() async {
    if (selectedChild == null) {
      showToastDanger(description: 'Por favor selecciona un niño');
      return;
    }

    isLoading = true;
    updateState();

    try {
      // Create pickup session
      PickupSession session = PickupSession(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        userId: currentUser?.id,
        selectedChild: selectedChild,
        transportMode: selectedTransportMode,
        startTime: DateTime.now(),
        status: 'started',
      );

      // Save session to storage
      await storageSave('current_pickup_session', session.toJson());

      // Navigate to route confirmation
      routeTo('/route-confirmation');

    } catch (e) {
      showToastDanger(description: 'Error al iniciar el proceso de recogida');
    } finally {
      isLoading = false;
      updateState();
    }
  }

  /// Logout user
  onLogout() async {
    await User().clear();
    await NyStorage.delete('current_pickup_session');
    routeTo('/login', removeUntilPredicate: (route) => false);
  }
}
