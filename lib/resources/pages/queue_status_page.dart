import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/controllers/queue_status_controller.dart';
import '/resources/widgets/safearea_widget.dart';
import '/bootstrap/extensions.dart';

class QueueStatusPage extends NyStatefulWidget<QueueStatusController> {
  static RouteView path = ("/queue-status", (_) => QueueStatusPage());

  QueueStatusPage({super.key}) : super(child: () => _QueueStatusPageState());
}

class _QueueStatusPageState extends NyPage<QueueStatusPage> {
  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Estado de la Cola",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: context.color.content,
          ),
        ),
        backgroundColor: context.color.surface,
        elevation: 0,
        automaticallyImplyLeading: false, // Remove back button
      ),
      body: widget.controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeAreaWidget(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Child Info
                    _buildChildInfoCard(context),
                    
                    const SizedBox(height: 32),
                    
                    // Queue Status Display
                    _buildQueueStatusCard(context),
                    
                    const SizedBox(height: 24),
                    
                    // Traffic Light Indicator
                    _buildTrafficLightIndicator(context),
                    
                    const SizedBox(height: 24),
                    
                    // Wait Time Card
                    _buildWaitTimeCard(context),
                    
                    const Spacer(),
                    
                    // Cancel Button
                    _buildCancelButton(context),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildChildInfoCard(BuildContext context) {
    String childName = widget.controller.currentSession?.selectedChild?.name ?? '';
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.color.primaryAccent.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.child_care,
            color: context.color.primaryAccent,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            "Recogiendo a $childName",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: context.color.content,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQueueStatusCard(BuildContext context) {
    int? position = widget.controller.currentSession?.queuePosition;
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            FontAwesomeIcons.car,
            size: 48,
            color: context.color.primaryAccent,
          ),
          const SizedBox(height: 16),
          Text(
            position != null && position > 0
                ? "Autos delante: $position"
                : "¡Es tu turno!",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: context.color.content,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            position != null && position > 0
                ? "Tu posición en la cola"
                : "Puedes avanzar al área de recogida",
            style: TextStyle(
              fontSize: 16,
              color: context.color.contentSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrafficLightIndicator(BuildContext context) {
    Color statusColor = widget.controller.getQueueStatusColor();
    String statusText = widget.controller.getQueueStatusText();
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            "Estado de proximidad",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: context.color.content,
            ),
          ),
          const SizedBox(height: 16),
          
          // Traffic Light
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTrafficLightCircle(
                context,
                Colors.red,
                statusColor == Colors.red,
              ),
              const SizedBox(width: 12),
              _buildTrafficLightCircle(
                context,
                Colors.orange,
                statusColor == Colors.orange,
              ),
              const SizedBox(width: 12),
              _buildTrafficLightCircle(
                context,
                Colors.green,
                statusColor == Colors.green,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          Text(
            statusText,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: statusColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrafficLightCircle(BuildContext context, Color color, bool isActive) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: isActive ? color : color.withOpacity(0.2),
        shape: BoxShape.circle,
        border: Border.all(
          color: color,
          width: 2,
        ),
      ),
      child: isActive
          ? Icon(
              Icons.circle,
              color: Colors.white,
              size: 20,
            )
          : null,
    );
  }

  Widget _buildWaitTimeCard(BuildContext context) {
    int? waitTime = widget.controller.currentSession?.estimatedWaitTime;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: context.color.primaryAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              Icons.access_time,
              color: context.color.primaryAccent,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Tiempo estimado",
                  style: TextStyle(
                    fontSize: 14,
                    color: context.color.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  waitTime != null && waitTime > 0
                      ? "$waitTime minutos"
                      : "¡Ahora!",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: context.color.content,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  "Si te equivocaste o cambias de opinión, puedes cancelar",
                  style: TextStyle(
                    fontSize: 14,
                    color: context.color.content,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        OutlinedButton(
          onPressed: widget.controller.onCancel,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text(
            "Cancelar recogida",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
