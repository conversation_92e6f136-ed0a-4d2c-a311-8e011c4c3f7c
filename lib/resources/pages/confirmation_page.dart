import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/controllers/confirmation_controller.dart';
import '/resources/widgets/safearea_widget.dart';
import '/bootstrap/extensions.dart';

class ConfirmationPage extends NyStatefulWidget<ConfirmationController> {
  static RouteView path = ("/confirmation", (_) => ConfirmationPage());

  ConfirmationPage({super.key}) : super(child: () => _ConfirmationPageState());
}

class _ConfirmationPageState extends NyPage<ConfirmationPage> {
  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "¡Listo!",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: context.color.content,
          ),
        ),
        backgroundColor: context.color.surface,
        elevation: 0,
        automaticallyImplyLeading: false, // Remove back button
      ),
      body: widget.controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeAreaWidget(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Spacer(),
                    
                    // Success Icon
                    _buildSuccessIcon(context),
                    
                    const SizedBox(height: 32),
                    
                    // Success Message
                    _buildSuccessMessage(context),
                    
                    const SizedBox(height: 24),
                    
                    // Completion Time
                    _buildCompletionTime(context),
                    
                    const SizedBox(height: 32),
                    
                    // Additional Info
                    _buildAdditionalInfo(context),
                    
                    const Spacer(),
                    
                    // Action Buttons
                    _buildActionButtons(context),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildSuccessIcon(BuildContext context) {
    return Center(
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.green.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: const Icon(
          Icons.check_circle,
          size: 80,
          color: Colors.green,
        ),
      ),
    );
  }

  Widget _buildSuccessMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            "¡Recogida Completada!",
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: context.color.content,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            widget.controller.confirmationMessage,
            style: TextStyle(
              fontSize: 18,
              color: context.color.content,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionTime(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: context.color.primaryAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              Icons.access_time,
              color: context.color.primaryAccent,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Tiempo total",
                  style: TextStyle(
                    fontSize: 14,
                    color: context.color.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.controller.completionTime,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: context.color.content,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.primaryAccent.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: context.color.primaryAccent,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  "Información importante",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: context.color.content,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            "• El colegio ha sido notificado de tu llegada\n"
            "• Tu hijo/a estará listo/a para la recogida\n"
            "• Dirígete al área de recogida designada\n"
            "• Que tengas un excelente día",
            style: TextStyle(
              fontSize: 14,
              color: context.color.content,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Primary Action Button
        ElevatedButton(
          onPressed: widget.controller.onFinish,
          style: ElevatedButton.styleFrom(
            backgroundColor: context.color.primaryAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 18),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: const Text(
            "Finalizar",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Secondary Action Button
        TextButton(
          onPressed: widget.controller.onReturnToMain,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            "Volver al inicio",
            style: TextStyle(
              fontSize: 16,
              color: context.color.contentSecondary,
            ),
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Thank You Message
        Container(
          padding: const EdgeInsets.all(16),
          child: Text(
            "¡Gracias por usar nuestra aplicación!",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: context.color.primaryAccent,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}
