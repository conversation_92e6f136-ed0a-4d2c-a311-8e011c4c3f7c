import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/controllers/main_controller.dart';
import '/app/models/child.dart';
import '/app/models/transport_mode.dart';
import '/resources/widgets/safearea_widget.dart';
import '/bootstrap/extensions.dart';

class MainPage extends NyStatefulWidget<MainController> {
  static RouteView path = ("/main", (_) => MainPage());

  MainPage({super.key}) : super(child: () => _MainPageState());
}

class _MainPageState extends NyPage<MainPage> {
  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Recogida Escolar",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: context.color.content,
          ),
        ),
        backgroundColor: context.color.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: widget.controller.onLogout,
            tooltip: "Cerrar sesión",
          ),
        ],
      ),
      body: widget.controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeAreaWidget(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Main Question
                    Container(
                      margin: const EdgeInsets.only(bottom: 32),
                      child: Text(
                        "¿A quién venís a buscar hoy?",
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: context.color.content,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // Children List
                    Expanded(
                      child: widget.controller.children.isEmpty
                          ? _buildEmptyState(context)
                          : ListView.builder(
                              itemCount: widget.controller.children.length,
                              itemBuilder: (context, index) {
                                Child child = widget.controller.children[index];
                                return _buildChildCard(context, child);
                              },
                            ),
                    ),

                    // Transport Mode Selection
                    if (widget.controller.selectedChild != null) ...[
                      const SizedBox(height: 24),
                      _buildTransportModeSelection(context),
                    ],

                    // Action Button
                    const SizedBox(height: 32),
                    _buildActionButton(context),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildChildCard(BuildContext context, Child child) {
    bool isSelected = child.isSelected ?? false;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: isSelected 
            ? context.color.primaryAccent.withOpacity(0.1)
            : context.color.surface,
        borderRadius: BorderRadius.circular(16),
        elevation: isSelected ? 4 : 2,
        child: InkWell(
          onTap: () => widget.controller.onSelectChild(child),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: isSelected
                  ? Border.all(color: context.color.primaryAccent, width: 2)
                  : null,
            ),
            child: Row(
              children: [
                // Child Icon
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: context.color.primaryAccent.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    child.iconPath == 'child_girl' 
                        ? Icons.girl 
                        : Icons.boy,
                    size: 32,
                    color: context.color.primaryAccent,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Child Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        child.name ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: context.color.content,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        child.grade ?? '',
                        style: TextStyle(
                          fontSize: 16,
                          color: context.color.contentSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Selection Indicator
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: context.color.primaryAccent,
                    size: 28,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransportModeSelection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "¿Cómo venís?",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: context.color.content,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTransportModeButton(
                  context,
                  TransportMode.car,
                  FontAwesomeIcons.car,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTransportModeButton(
                  context,
                  TransportMode.walking,
                  FontAwesomeIcons.personWalking,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransportModeButton(
    BuildContext context,
    TransportMode mode,
    IconData icon,
  ) {
    bool isSelected = widget.controller.selectedTransportMode == mode;
    
    return Material(
      color: isSelected 
          ? context.color.primaryAccent
          : context.color.surfaceBackground,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => widget.controller.onChangeTransportMode(mode),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            children: [
              FaIcon(
                icon,
                size: 24,
                color: isSelected 
                    ? Colors.white 
                    : context.color.content,
              ),
              const SizedBox(height: 8),
              Text(
                mode.displayName,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isSelected 
                      ? Colors.white 
                      : context.color.content,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    bool canProceed = widget.controller.selectedChild != null;
    
    return ElevatedButton(
      onPressed: canProceed && !widget.controller.isLoading
          ? widget.controller.onStartPickup
          : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: context.color.primaryAccent,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 20),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 4,
      ),
      child: widget.controller.isLoading
          ? const SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              "Estoy en camino",
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.family_restroom,
            size: 80,
            color: context.color.contentSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            "No hay niños registrados",
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: context.color.content,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "Contacta al colegio para registrar a tus hijos",
            style: TextStyle(
              fontSize: 16,
              color: context.color.contentSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
