import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/controllers/route_confirmation_controller.dart';
import '/app/models/transport_mode.dart';
import '/resources/widgets/safearea_widget.dart';
import '/bootstrap/extensions.dart';

class RouteConfirmationPage extends NyStatefulWidget<RouteConfirmationController> {
  static RouteView path = ("/route-confirmation", (_) => RouteConfirmationPage());

  RouteConfirmationPage({super.key}) : super(child: () => _RouteConfirmationPageState());
}

class _RouteConfirmationPageState extends NyPage<RouteConfirmationPage> {
  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Confirmación de Ruta",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: context.color.content,
          ),
        ),
        backgroundColor: context.color.surface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => routeTo('/main'),
        ),
      ),
      body: widget.controller.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SafeAreaWidget(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Child Info Card
                    _buildChildInfoCard(context),
                    
                    const SizedBox(height: 24),
                    
                    // Transport Mode Display
                    _buildTransportModeCard(context),
                    
                    const SizedBox(height: 24),
                    
                    // Location/Status Card
                    if (widget.controller.currentSession?.transportMode == TransportMode.car)
                      _buildLocationCard(context)
                    else
                      _buildWalkingStatusCard(context),
                    
                    const Spacer(),
                    
                    // Action Buttons
                    _buildActionButtons(context),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildChildInfoCard(BuildContext context) {
    String childName = widget.controller.currentSession?.selectedChild?.name ?? '';
    String childGrade = widget.controller.currentSession?.selectedChild?.grade ?? '';
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.primaryAccent.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.color.primaryAccent.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: context.color.primaryAccent.withOpacity(0.2),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.child_care,
              size: 32,
              color: context.color.primaryAccent,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Recogiendo a:",
                  style: TextStyle(
                    fontSize: 14,
                    color: context.color.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  childName,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: context.color.content,
                  ),
                ),
                Text(
                  childGrade,
                  style: TextStyle(
                    fontSize: 16,
                    color: context.color.contentSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransportModeCard(BuildContext context) {
    TransportMode? mode = widget.controller.currentSession?.transportMode;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: context.color.primaryAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              mode == TransportMode.car 
                  ? FontAwesomeIcons.car 
                  : FontAwesomeIcons.personWalking,
              size: 24,
              color: context.color.primaryAccent,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Modo de transporte:",
                  style: TextStyle(
                    fontSize: 14,
                    color: context.color.contentSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  mode?.displayName ?? '',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.color.content,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.color.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: context.color.primaryAccent,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                "Tu ubicación",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: context.color.content,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            widget.controller.userLocation ?? "Obteniendo ubicación...",
            style: TextStyle(
              fontSize: 16,
              color: context.color.contentSecondary,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.color.primaryAccent.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: context.color.primaryAccent,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.controller.confirmationMessage,
                    style: TextStyle(
                      fontSize: 14,
                      color: context.color.content,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWalkingStatusCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.green.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            widget.controller.confirmationMessage,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: context.color.content,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            "El colegio será notificado de tu llegada",
            style: TextStyle(
              fontSize: 14,
              color: context.color.contentSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Main Action Button
        ElevatedButton(
          onPressed: widget.controller.isLoading
              ? null
              : widget.controller.onNotifySchool,
          style: ElevatedButton.styleFrom(
            backgroundColor: context.color.primaryAccent,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
          child: widget.controller.isLoading
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  widget.controller.buttonText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
        
        const SizedBox(height: 12),
        
        // Cancel Button
        TextButton(
          onPressed: widget.controller.isLoading
              ? null
              : widget.controller.onCancel,
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Text(
            "Cancelar",
            style: TextStyle(
              fontSize: 16,
              color: context.color.contentSecondary,
            ),
          ),
        ),
      ],
    );
  }
}
