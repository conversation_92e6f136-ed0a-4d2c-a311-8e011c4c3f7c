import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/controllers/login_controller.dart';
import '/resources/widgets/safearea_widget.dart';
import '/bootstrap/extensions.dart';

class LoginPage extends NyStatefulWidget<LoginController> {
  static RouteView path = ("/login", (_) => LoginPage());

  LoginPage({super.key}) : super(child: () => _LoginPageState());
}

class _LoginPageState extends NyPage<LoginPage> {
  final _formKey = GlobalKey<FormState>();

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              context.color.primaryAccent.withOpacity(0.1),
              context.color.surfaceBackground,
            ],
          ),
        ),
        child: SafeAreaWidget(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // App Logo/Title
                  Container(
                    margin: const EdgeInsets.only(bottom: 48),
                    child: Column(
                      children: [
                        Icon(
                          Icons.school,
                          size: 80,
                          color: context.color.primaryAccent,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          "Recogida Escolar",
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: context.color.content,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "Conecta con el colegio de manera fácil",
                          style: TextStyle(
                            fontSize: 16,
                            color: context.color.contentSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  // Phone Input Card
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: context.color.surface,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Text(
                          "Número de teléfono",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: context.color.content,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Phone Input Field
                        TextFormField(
                          controller: widget.controller.phoneController,
                          keyboardType: TextInputType.phone,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          style: const TextStyle(fontSize: 18),
                          decoration: InputDecoration(
                            hintText: "Ej: 1234567890",
                            prefixIcon: const Icon(Icons.phone),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: context.color.surfaceBackground,
                          ),
                          validator: widget.controller.validatePhone,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Remember Phone Checkbox
                        Row(
                          children: [
                            Checkbox(
                              value: widget.controller.rememberPhone,
                              onChanged: widget.controller.onToggleRememberPhone,
                            ),
                            Expanded(
                              child: Text(
                                "Recordar mi número",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: context.color.content,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Login Button
                  ElevatedButton(
                    onPressed: widget.controller.isLoading 
                        ? null 
                        : () {
                            if (_formKey.currentState!.validate()) {
                              widget.controller.onLogin();
                            }
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.color.primaryAccent,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: widget.controller.isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            "Ingresar con número de teléfono",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),

                  const SizedBox(height: 24),

                  // Info Text
                  Text(
                    "Tu número será usado para identificarte en el sistema del colegio",
                    style: TextStyle(
                      fontSize: 14,
                      color: context.color.contentSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
