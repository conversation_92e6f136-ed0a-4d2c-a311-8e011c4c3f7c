import 'package:flutter/material.dart';

/* Default text theme
|-------------------------------------------------------------------------- */

// Larger fonts for better accessibility and readability in a school pickup app
const TextTheme defaultTextTheme = TextTheme(
  titleLarge: TextStyle(
    fontSize: 24.0, // Increased from 20
    fontWeight: FontWeight.w600,
  ),
  headlineSmall: TextStyle(
    fontSize: 26.0, // Increased from 22
    fontWeight: FontWeight.w500,
  ),
  headlineMedium: TextStyle(
    fontSize: 28.0, // Increased from 24
    fontWeight: FontWeight.w600,
  ),
  displaySmall: TextStyle(
    fontSize: 30.0, // Increased from 26
    fontWeight: FontWeight.w700,
  ),
  displayMedium: TextStyle(
    fontSize: 32.0, // Increased from 28
    fontWeight: FontWeight.w600,
  ),
  displayLarge: TextStyle(
    fontSize: 40.0, // Increased from 36
    fontWeight: FontWeight.w300,
  ),
  titleSmall: TextStyle(
    fontSize: 16.0, // Increased from 14
    fontWeight: FontWeight.w500,
  ),
  titleMedium: TextStyle(
    fontSize: 18.0, // Increased from 16
    fontWeight: FontWeight.w500,
  ),
  labelSmall: TextStyle(
    fontSize: 12.0, // Increased from 10
    fontWeight: FontWeight.w400,
  ),
  labelLarge: TextStyle(
    fontSize: 16.0, // Added explicit size
    fontWeight: FontWeight.w500,
  ),
  bodyLarge: TextStyle(
    fontSize: 20.0, // Increased from 18
    fontWeight: FontWeight.w400,
  ),
  bodyMedium: TextStyle(
    fontSize: 17.0, // Increased from 15.5
    fontWeight: FontWeight.w400,
  ),
  bodySmall: TextStyle(
    fontSize: 15.0, // Increased from 13
    fontWeight: FontWeight.w400,
  ),
);
