import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Light Theme Colors
|-------------------------------------------------------------------------- */

class LightThemeColors implements ColorStyles {
  // general - soft, warm colors for a school pickup app
  @override
  Color get background => const Color(0xFFF8F9FA); // Very light gray-blue

  @override
  Color get content => const Color(0xFF2D3748); // Soft dark gray instead of black
  @override
  Color get primaryAccent => const Color(0xFF4299E1); // Soft blue

  // Additional content colors for better hierarchy
  @override
  Color get contentSecondary => const Color(0xFF718096); // Medium gray for secondary text
  @override
  Color get surface => const Color(0xFFFFFFFF); // Pure white for cards

  @override
  Color get surfaceBackground => const Color(0xFFFFFFFF); // Pure white for cards
  @override
  Color get surfaceContent => const Color(0xFF2D3748); // Consistent with content

  // app bar - soft and welcoming
  @override
  Color get appBarBackground => const Color(0xFFFFFFFF); // White app bar
  @override
  Color get appBarPrimaryContent => const Color(0xFF2D3748); // Dark text on white

  // buttons - friendly and accessible
  @override
  Color get buttonBackground => const Color(0xFF4299E1); // Soft blue
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // White text

  @override
  Color get buttonSecondaryBackground => const Color(0xFFE2E8F0); // Light gray
  @override
  Color get buttonSecondaryContent => const Color(0xFF4A5568); // Medium gray

  // bottom tab bar - soft and accessible
  @override
  Color get bottomTabBarBackground => const Color(0xFFFFFFFF); // White

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFF4299E1); // Soft blue
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFF718096); // Medium gray

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFF718096); // Medium gray
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFF2D3748); // Dark gray

  // toast notification
  @override
  Color get toastNotificationBackground => const Color(0xFFFFFFFF); // White
}
