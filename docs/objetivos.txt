Utilizar buenas prácticas para el diseño de la aplicación
Seguir convención actual de la estructura del proyecto.
Este proyecto fue basado en un micro-framework de Flutter, llamado Nylo.
Estas son las posibles pantallas de la aplicación:

👤 Pantalla de Inicio (Login simple)

Botón grande de "Ingresar con número de teléfono"

Opción para recordar el número

Fondo con una imagen tranquila (como una puerta del colegio o niños saliendo felices)

🏠 Pantalla Principal

Título: “¿A quién venís a buscar hoy?”

Lista con botones grandes de los niños vinculados al usuario (nombre + grado + ícono)

Botón destacado: “Estoy en camino”

Botón para cambiar a “Vengo caminando” o “Vengo en auto”

📍 Pantalla de Confirmación de Ruta

Si el usuario viene en auto:
Muestra su ubicación y botón: “Avisar al colegio”

Si viene a pie:
Mensaje tipo “Avisamos que estás cerca. ¡Nos preparamos!”

🚗 Pantalla de Estado de la Cola

Muestra:

Número de autos delante

Tiempo estimado de atención

Un mapa opcional o gráfico simple tipo semáforo: “Lejos / Cerca / Muy cerca”

Botón para cancelar si se equivoca

✅ Pantalla de Confirmación

Mensaje: “Ya avisamos al colegio. Prepararemos a [nombre del niño/a].”

Botón: “Finalizar” o “Volver al inicio”

Diseño visual:

Letra grande, colores suaves

Íconos claros (auto, caminando, niño, reloj)

Menú muy limitado, o sin menú: todo en flujo de pasos